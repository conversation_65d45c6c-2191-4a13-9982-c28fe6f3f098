import React, { useEffect, useState } from 'react';
import { useNavigate, useLocation } from 'react-router-dom';

// Navigation Component
const Navigation = () => {
  const navigate = useNavigate();
  
  return (
    <nav style={{
      backgroundColor: '#1d3557',
      padding: '1rem 0',
      boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
    }}>
      <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 10px' }}>
        <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
          <div style={{
            display: 'flex',
            alignItems: 'center',
            gap: '0.3rem',
            marginLeft: '-135px',
            paddingLeft: '15px'
          }}>
            <span style={{
              fontSize: '1.8rem',
              color: '#e63946',
              display: 'flex',
              alignItems: 'center',
              marginTop: '-2px'
            }}>🩸</span>
            <h1 style={{
              color: 'white',
              margin: 0,
              fontSize: '1.8rem',
              fontWeight: 'bold',
              display: 'flex',
              alignItems: 'center',
              letterSpacing: '0.5px'
            }}>
              <span style={{ color: '#e63946' }}>VITAL</span>
              <span style={{ color: 'white' }}>DROP</span>
            </h1>
          </div>
          <div style={{ display: 'flex', gap: '20px', alignItems: 'center', marginRight: '-7px', paddingRight: '20px' }}>
            <a href="/" style={{
              color: 'white',
              textDecoration: 'none',
              padding: '10px 16px',
              borderRadius: '8px',
              fontSize: '0.95rem',
              fontWeight: '500',
              transition: 'all 0.3s ease'
            }}>Home</a>
            <a href="/about" style={{
              color: 'white',
              textDecoration: 'none',
              padding: '10px 16px',
              borderRadius: '8px',
              fontSize: '0.95rem',
              fontWeight: '500',
              transition: 'all 0.3s ease'
            }}>About</a>
            <a href="/donate" style={{
              color: 'white',
              textDecoration: 'none',
              padding: '10px 16px',
              borderRadius: '8px',
              fontSize: '0.95rem',
              fontWeight: '500',
              transition: 'all 0.3s ease'
            }}>Donate</a>
            <a href="/schedule" style={{
              color: 'white',
              textDecoration: 'none',
              padding: '10px 16px',
              borderRadius: '8px',
              fontSize: '0.95rem',
              fontWeight: '500',
              transition: 'all 0.3s ease'
            }}>Schedule</a>
            <a href="/blood-banks" style={{
              color: 'white',
              textDecoration: 'none',
              padding: '10px 16px',
              borderRadius: '8px',
              fontSize: '0.95rem',
              fontWeight: '500',
              transition: 'all 0.3s ease',
              whiteSpace: 'nowrap'
            }}>Find Blood Banks</a>
            <a href="/notifications" style={{
              color: 'white',
              textDecoration: 'none',
              padding: '10px 12px',
              borderRadius: '8px',
              fontSize: '1.2rem',
              fontWeight: '500',
              transition: 'all 0.3s ease',
              position: 'relative',
              display: 'flex',
              alignItems: 'center'
            }}>
              🔔
              <span style={{
                position: 'absolute',
                top: '6px',
                right: '8px',
                width: '8px',
                height: '8px',
                backgroundColor: '#ef4444',
                borderRadius: '50%',
                border: '1px solid white'
              }}></span>
            </a>
            <a href="/login" style={{
              color: 'white',
              textDecoration: 'none',
              padding: '10px 16px',
              borderRadius: '8px',
              fontSize: '0.95rem',
              fontWeight: '500',
              transition: 'all 0.3s ease'
            }}>Login</a>
            <a href="/signup" style={{
              background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
              color: 'white',
              textDecoration: 'none',
              padding: '12px 24px',
              borderRadius: '12px',
              fontSize: '0.95rem',
              fontWeight: '600',
              letterSpacing: '0.025em',
              boxShadow: '0 4px 15px rgba(239, 68, 68, 0.3), 0 2px 8px rgba(0,0,0,0.1)',
              border: '1px solid rgba(255,255,255,0.1)',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
            }}>Sign Up</a>
            <a href="/admin" style={{
              background: 'linear-gradient(135deg, #64748b 0%, #475569 100%)',
              color: 'white',
              textDecoration: 'none',
              padding: '12px 24px',
              borderRadius: '12px',
              fontSize: '0.95rem',
              fontWeight: '600',
              letterSpacing: '0.025em',
              boxShadow: '0 4px 15px rgba(100, 116, 139, 0.3), 0 2px 8px rgba(0,0,0,0.1)',
              border: '1px solid rgba(255,255,255,0.1)',
              transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
            }}>Admin</a>
          </div>
        </div>
      </div>
    </nav>
  );
};

const ThankYou = () => {
  const navigate = useNavigate();
  const location = useLocation();
  const [animationStep, setAnimationStep] = useState(0);
  
  // Get appointment details from navigation state
  const appointmentDetails = location.state || {};

  useEffect(() => {
    // Animation sequence
    const timer1 = setTimeout(() => setAnimationStep(1), 500);
    const timer2 = setTimeout(() => setAnimationStep(2), 1000);
    const timer3 = setTimeout(() => setAnimationStep(3), 1500);
    
    return () => {
      clearTimeout(timer1);
      clearTimeout(timer2);
      clearTimeout(timer3);
    };
  }, []);

  const handleGoHome = () => {
    navigate('/');
  };

  const handleScheduleAnother = () => {
    navigate('/schedule');
  };

  return (
    <>
      <Navigation />
      <div style={{
        minHeight: '100vh',
        background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
        padding: '40px 20px',
        display: 'flex',
        alignItems: 'center',
        justifyContent: 'center'
      }}>
        <div style={{
          maxWidth: '800px',
          width: '100%',
          background: 'white',
          borderRadius: '25px',
          padding: '60px 40px',
          textAlign: 'center',
          boxShadow: '0 20px 60px rgba(0,0,0,0.1)',
          border: '1px solid #e5e7eb'
        }}>
          {/* Animated Success Icon */}
          <div style={{
            fontSize: '5rem',
            marginBottom: '30px',
            transform: animationStep >= 1 ? 'scale(1)' : 'scale(0)',
            transition: 'transform 0.5s cubic-bezier(0.68, -0.55, 0.265, 1.55)',
            filter: animationStep >= 1 ? 'none' : 'blur(10px)'
          }}>
            🎉
          </div>

          {/* Main Thank You Message */}
          <div style={{
            opacity: animationStep >= 2 ? 1 : 0,
            transform: animationStep >= 2 ? 'translateY(0)' : 'translateY(20px)',
            transition: 'all 0.6s ease-out',
            marginBottom: '40px'
          }}>
            <h1 style={{
              fontSize: '3rem',
              fontWeight: '800',
              background: 'linear-gradient(135deg, #dc3545 0%, #e63946 100%)',
              WebkitBackgroundClip: 'text',
              WebkitTextFillColor: 'transparent',
              marginBottom: '20px',
              lineHeight: '1.2'
            }}>
              Thank You, Hero!
            </h1>
            
            <p style={{
              fontSize: '1.4rem',
              color: '#374151',
              lineHeight: '1.6',
              marginBottom: '30px',
              fontWeight: '500'
            }}>
              Your blood donation appointment has been successfully scheduled!
            </p>
            
            <p style={{
              fontSize: '1.1rem',
              color: '#6b7280',
              lineHeight: '1.6',
              maxWidth: '600px',
              margin: '0 auto'
            }}>
              You're about to make a life-changing difference. Your single donation can save up to 3 lives. 
              We'll send you a confirmation email with all the details shortly.
            </p>
          </div>

          {/* Appointment Details Card */}
          {appointmentDetails.name && (
            <div style={{
              opacity: animationStep >= 3 ? 1 : 0,
              transform: animationStep >= 3 ? 'translateY(0)' : 'translateY(30px)',
              transition: 'all 0.6s ease-out 0.2s',
              background: 'linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%)',
              borderRadius: '20px',
              padding: '30px',
              marginBottom: '40px',
              border: '2px solid #dc3545'
            }}>
              <h3 style={{
                fontSize: '1.5rem',
                color: '#1f2937',
                marginBottom: '25px',
                fontWeight: '700'
              }}>
                📋 Your Appointment Details
              </h3>
              
              <div style={{
                display: 'grid',
                gridTemplateColumns: 'repeat(auto-fit, minmax(250px, 1fr))',
                gap: '20px',
                textAlign: 'left'
              }}>
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <span style={{ fontSize: '1.5rem' }}>👤</span>
                  <div>
                    <strong style={{ color: '#374151' }}>Name:</strong>
                    <div style={{ color: '#6b7280' }}>{appointmentDetails.name}</div>
                  </div>
                </div>
                
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <span style={{ fontSize: '1.5rem' }}>🩸</span>
                  <div>
                    <strong style={{ color: '#374151' }}>Blood Type:</strong>
                    <div style={{ color: '#6b7280' }}>{appointmentDetails.bloodType}</div>
                  </div>
                </div>
                
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <span style={{ fontSize: '1.5rem' }}>📅</span>
                  <div>
                    <strong style={{ color: '#374151' }}>Date:</strong>
                    <div style={{ color: '#6b7280' }}>{appointmentDetails.date}</div>
                  </div>
                </div>
                
                <div style={{ display: 'flex', alignItems: 'center', gap: '12px' }}>
                  <span style={{ fontSize: '1.5rem' }}>⏰</span>
                  <div>
                    <strong style={{ color: '#374151' }}>Time:</strong>
                    <div style={{ color: '#6b7280' }}>{appointmentDetails.time}</div>
                  </div>
                </div>
              </div>
            </div>
          )}

          {/* Action Buttons */}
          <div style={{
            opacity: animationStep >= 3 ? 1 : 0,
            transform: animationStep >= 3 ? 'translateY(0)' : 'translateY(30px)',
            transition: 'all 0.6s ease-out 0.4s',
            display: 'flex',
            gap: '20px',
            justifyContent: 'center',
            flexWrap: 'wrap'
          }}>
            <button
              onClick={handleGoHome}
              style={{
                background: 'linear-gradient(135deg, #dc3545 0%, #c82333 100%)',
                color: 'white',
                border: 'none',
                padding: '15px 30px',
                borderRadius: '15px',
                fontSize: '1.1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease',
                boxShadow: '0 8px 25px rgba(220, 53, 69, 0.3)'
              }}
              onMouseEnter={(e) => {
                e.target.style.transform = 'translateY(-3px)';
                e.target.style.boxShadow = '0 12px 35px rgba(220, 53, 69, 0.4)';
              }}
              onMouseLeave={(e) => {
                e.target.style.transform = 'translateY(0)';
                e.target.style.boxShadow = '0 8px 25px rgba(220, 53, 69, 0.3)';
              }}
            >
              🏠 Go to Home
            </button>
            
            <button
              onClick={handleScheduleAnother}
              style={{
                background: 'transparent',
                color: '#dc3545',
                border: '2px solid #dc3545',
                padding: '15px 30px',
                borderRadius: '15px',
                fontSize: '1.1rem',
                fontWeight: '600',
                cursor: 'pointer',
                transition: 'all 0.3s ease'
              }}
              onMouseEnter={(e) => {
                e.target.style.background = '#dc3545';
                e.target.style.color = 'white';
                e.target.style.transform = 'translateY(-3px)';
              }}
              onMouseLeave={(e) => {
                e.target.style.background = 'transparent';
                e.target.style.color = '#dc3545';
                e.target.style.transform = 'translateY(0)';
              }}
            >
              📅 Schedule Another
            </button>
          </div>

          {/* Inspirational Message */}
          <div style={{
            marginTop: '40px',
            padding: '25px',
            background: 'linear-gradient(135deg, #d4edda 0%, #c3e6cb 100%)',
            borderRadius: '15px',
            border: '2px solid #28a745'
          }}>
            <p style={{
              fontSize: '1.1rem',
              color: '#155724',
              margin: 0,
              fontWeight: '600',
              lineHeight: '1.6'
            }}>
              💝 "The gift of blood is the gift of life. Thank you for being someone's hero today!"
            </p>
          </div>
        </div>
      </div>
    </>
  );
};

export default ThankYou;
