import React, { useState, useEffect } from 'react';
import './DonorNotifications.css';

// Navigation Component
const Navigation = () => (
  <nav style={{
    backgroundColor: '#1d3557',
    padding: '1rem 0',
    boxShadow: '0 2px 4px rgba(0,0,0,0.1)'
  }}>
    <div style={{ maxWidth: '1200px', margin: '0 auto', padding: '0 10px' }}>
      <div style={{ display: 'flex', justifyContent: 'space-between', alignItems: 'center' }}>
        <div style={{
          display: 'flex',
          alignItems: 'center',
          gap: '0.3rem',
          marginLeft: '-135px',
          paddingLeft: '15px'
        }}>
          <span style={{
            fontSize: '1.8rem',
            color: '#e63946',
            display: 'flex',
            alignItems: 'center',
            marginTop: '-2px'
          }}>🩸</span>
          <h1 style={{
            color: 'white',
            margin: 0,
            fontSize: '1.8rem',
            fontWeight: 'bold',
            display: 'flex',
            alignItems: 'center',
            letterSpacing: '0.5px'
          }}>
            <span style={{ color: '#e63946' }}>VITAL</span>
            <span style={{ color: 'white' }}>DROP</span>
          </h1>
        </div>
        <div style={{ display: 'flex', gap: '20px', alignItems: 'center', marginRight: '-7px', paddingRight: '20px' }}>
          <a href="/" style={{
            color: 'white',
            textDecoration: 'none',
            padding: '10px 16px',
            borderRadius: '8px',
            fontSize: '0.95rem',
            fontWeight: '500',
            transition: 'all 0.3s ease'
          }}>Home</a>
          <a href="/about" style={{
            color: 'white',
            textDecoration: 'none',
            padding: '10px 16px',
            borderRadius: '8px',
            fontSize: '0.95rem',
            fontWeight: '500',
            transition: 'all 0.3s ease'
          }}>About</a>
          <a href="/donate" style={{
            color: 'white',
            textDecoration: 'none',
            padding: '10px 16px',
            borderRadius: '8px',
            fontSize: '0.95rem',
            fontWeight: '500',
            transition: 'all 0.3s ease'
          }}>Donate</a>
          <a href="/schedule" style={{
            color: 'white',
            textDecoration: 'none',
            padding: '10px 16px',
            borderRadius: '8px',
            fontSize: '0.95rem',
            fontWeight: '500',
            transition: 'all 0.3s ease'
          }}>Schedule</a>
          <a href="/blood-banks" style={{
            color: 'white',
            textDecoration: 'none',
            padding: '10px 16px',
            borderRadius: '8px',
            fontSize: '0.95rem',
            fontWeight: '500',
            transition: 'all 0.3s ease',
            whiteSpace: 'nowrap'
          }}>Find Blood Banks</a>
          <a href="/notifications" style={{
            color: 'white',
            textDecoration: 'none',
            padding: '10px 12px',
            borderRadius: '8px',
            fontSize: '1.2rem',
            fontWeight: '500',
            transition: 'all 0.3s ease',
            position: 'relative',
            display: 'flex',
            alignItems: 'center',
            backgroundColor: 'rgba(255,255,255,0.1)'
          }}>
            🔔
            <span style={{
              position: 'absolute',
              top: '6px',
              right: '8px',
              width: '8px',
              height: '8px',
              backgroundColor: '#ef4444',
              borderRadius: '50%',
              border: '1px solid white'
            }}></span>
          </a>
          <a href="/login" style={{
            color: 'white',
            textDecoration: 'none',
            padding: '10px 16px',
            borderRadius: '8px',
            fontSize: '0.95rem',
            fontWeight: '500',
            transition: 'all 0.3s ease'
          }}>Login</a>
          <a href="/signup" style={{
            background: 'linear-gradient(135deg, #ef4444 0%, #dc2626 100%)',
            color: 'white',
            textDecoration: 'none',
            padding: '12px 24px',
            borderRadius: '12px',
            fontSize: '0.95rem',
            fontWeight: '600',
            letterSpacing: '0.025em',
            boxShadow: '0 4px 15px rgba(239, 68, 68, 0.3), 0 2px 8px rgba(0,0,0,0.1)',
            border: '1px solid rgba(255,255,255,0.1)',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
          }}>Sign Up</a>
          <a href="/admin" style={{
            background: 'linear-gradient(135deg, #64748b 0%, #475569 100%)',
            color: 'white',
            textDecoration: 'none',
            padding: '12px 24px',
            borderRadius: '12px',
            fontSize: '0.95rem',
            fontWeight: '600',
            letterSpacing: '0.025em',
            boxShadow: '0 4px 15px rgba(100, 116, 139, 0.3), 0 2px 8px rgba(0,0,0,0.1)',
            border: '1px solid rgba(255,255,255,0.1)',
            transition: 'all 0.3s cubic-bezier(0.4, 0, 0.2, 1)'
          }}>Admin</a>
        </div>
      </div>
    </div>
  </nav>
);

const DonorNotifications = () => {
  const [notifications, setNotifications] = useState([
    {
      id: 1,
      type: 'emergency',
      title: 'URGENT: Blood Needed',
      message: 'Emergency request for O+ blood at City Hospital. Patient in critical condition.',
      bloodType: 'O+',
      location: 'City Hospital, Downtown',
      urgency: 'critical',
      timeAgo: '2 minutes ago',
      unitsNeeded: 3,
      isRead: false,
      requestId: 'ER001'
    },
    {
      id: 2,
      type: 'reminder',
      title: 'Donation Reminder',
      message: 'You are eligible to donate blood again. Your last donation was 3 months ago.',
      timeAgo: '1 hour ago',
      isRead: false
    },
    {
      id: 3,
      type: 'match',
      title: 'You\'re a Match!',
      message: 'Your blood type A+ is needed at Regional Medical Center.',
      bloodType: 'A+',
      location: 'Regional Medical Center',
      urgency: 'high',
      timeAgo: '3 hours ago',
      unitsNeeded: 2,
      isRead: true,
      requestId: 'ER002'
    },
    {
      id: 4,
      type: 'reward',
      title: 'Reward Earned!',
      message: 'You earned 50 points for your recent donation. Thank you for saving lives!',
      timeAgo: '1 day ago',
      points: 50,
      isRead: true
    }
  ]);

  const [filter, setFilter] = useState('all');

  const getNotificationIcon = (type) => {
    switch (type) {
      case 'emergency': return '🚨';
      case 'reminder': return '⏰';
      case 'match': return '🎯';
      case 'reward': return '🏆';
      default: return '📢';
    }
  };

  const getUrgencyColor = (urgency) => {
    switch (urgency) {
      case 'critical': return '#ef4444';
      case 'high': return '#f59e0b';
      case 'medium': return '#3b82f6';
      default: return '#64748b';
    }
  };

  const markAsRead = (id) => {
    setNotifications(prev => 
      prev.map(notif => 
        notif.id === id ? { ...notif, isRead: true } : notif
      )
    );
  };

  const markAllAsRead = () => {
    setNotifications(prev => 
      prev.map(notif => ({ ...notif, isRead: true }))
    );
  };

  const deleteNotification = (id) => {
    setNotifications(prev => prev.filter(notif => notif.id !== id));
  };

  const respondToEmergency = (requestId, response) => {
    alert(`Response "${response}" sent for emergency request ${requestId}`);
    // Here you would typically send the response to the backend
  };

  const filteredNotifications = notifications.filter(notif => {
    if (filter === 'all') return true;
    if (filter === 'unread') return !notif.isRead;
    return notif.type === filter;
  });

  const unreadCount = notifications.filter(n => !n.isRead).length;

  return (
    <>
      <Navigation />
      <div className="donor-notifications">
      <div className="notifications-header">
        <div className="header-title">
          <h2>Notifications</h2>
          <span className="notification-count">{unreadCount} unread</span>
        </div>
        <div className="header-actions">
          <button className="mark-all-read-btn" onClick={markAllAsRead}>
            Mark All Read
          </button>
        </div>
      </div>

      <div className="notification-filters">
        <button 
          className={`filter-btn ${filter === 'all' ? 'active' : ''}`}
          onClick={() => setFilter('all')}
        >
          All
        </button>
        <button 
          className={`filter-btn ${filter === 'unread' ? 'active' : ''}`}
          onClick={() => setFilter('unread')}
        >
          Unread ({unreadCount})
        </button>
        <button 
          className={`filter-btn ${filter === 'emergency' ? 'active' : ''}`}
          onClick={() => setFilter('emergency')}
        >
          Emergency
        </button>
        <button 
          className={`filter-btn ${filter === 'match' ? 'active' : ''}`}
          onClick={() => setFilter('match')}
        >
          Matches
        </button>
        <button 
          className={`filter-btn ${filter === 'reminder' ? 'active' : ''}`}
          onClick={() => setFilter('reminder')}
        >
          Reminders
        </button>
      </div>

      <div className="notifications-list">
        {filteredNotifications.length === 0 ? (
          <div className="no-notifications">
            <div className="no-notif-icon">📭</div>
            <h3>No notifications found</h3>
            <p>You're all caught up!</p>
          </div>
        ) : (
          filteredNotifications.map(notification => (
            <div 
              key={notification.id} 
              className={`notification-card ${notification.type} ${!notification.isRead ? 'unread' : ''}`}
              onClick={() => markAsRead(notification.id)}
            >
              <div className="notification-icon">
                {getNotificationIcon(notification.type)}
              </div>
              
              <div className="notification-content">
                <div className="notification-header">
                  <h4 className="notification-title">{notification.title}</h4>
                  <span className="notification-time">{notification.timeAgo}</span>
                </div>
                
                <p className="notification-message">{notification.message}</p>
                
                {notification.bloodType && (
                  <div className="notification-details">
                    <span className="blood-type-badge">{notification.bloodType}</span>
                    <span className="location">{notification.location}</span>
                    {notification.unitsNeeded && (
                      <span className="units-needed">{notification.unitsNeeded} units needed</span>
                    )}
                  </div>
                )}
                
                {notification.urgency && (
                  <div className="urgency-indicator" style={{ backgroundColor: getUrgencyColor(notification.urgency) }}>
                    {notification.urgency.toUpperCase()}
                  </div>
                )}
                
                {notification.points && (
                  <div className="points-earned">
                    +{notification.points} points earned
                  </div>
                )}
                
                {notification.type === 'emergency' && notification.requestId && (
                  <div className="emergency-actions">
                    <button 
                      className="respond-btn accept"
                      onClick={(e) => {
                        e.stopPropagation();
                        respondToEmergency(notification.requestId, 'accept');
                      }}
                    >
                      ✓ I Can Help
                    </button>
                    <button 
                      className="respond-btn decline"
                      onClick={(e) => {
                        e.stopPropagation();
                        respondToEmergency(notification.requestId, 'decline');
                      }}
                    >
                      ✗ Not Available
                    </button>
                  </div>
                )}
              </div>
              
              <button 
                className="delete-btn"
                onClick={(e) => {
                  e.stopPropagation();
                  deleteNotification(notification.id);
                }}
              >
                ×
              </button>
            </div>
          ))
        )}
      </div>
    </div>
    </>
  );
};

export default DonorNotifications;
